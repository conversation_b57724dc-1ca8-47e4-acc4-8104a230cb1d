import BasicButton from '@/components/Commons/BasicButton';
import BasicInput from '@/components/Commons/BasicInput';
import BasicPagination from '@/components/Commons/BasicPagination';
import BasicSelect from '@/components/Commons/BasicSelect';
import { openNotificationFail } from '@/components/Notification';
import SearchSVG from '@/components/SVG/SearchSVG';
import { PREFECTURES_OPTIONS } from '@/constants/prefectures';
import STATUS_CODE from '@/constants/statusCode';
import useFetchCity from '@/hooks/useFetchCity';
import { Form, Select, Tooltip } from 'antd';
import { useCallback, useContext, useEffect, useState } from 'react';
import { PlanCreateContext } from '@/providers';
import type { ColumnsType } from 'antd/lib/table';
import { PlusOutlined } from '@ant-design/icons';
import BasicTable from '@/components/Commons/BasicTable';
import { PLAN_TABS } from '@/constants/plan';
import { formatDateYMD } from '@/utils/date';
import { ITEM_PER_PAGE, sortOrderOption } from '@/utils/constants';
import type { TouristSpotDetailType } from '@/apis/purchase-and-material-management/touristDestinations';
import { getListTouristSpot } from '@/apis/purchase-and-material-management/touristDestinations';
import { formatMoney } from '@/utils';

const initialSearch = {
  page: 1,
  limit: ITEM_PER_PAGE,
  order: sortOrderOption?.[0]?.value,
  sort: 'parking_fee',
};

function TravelSpot() {
  const [form] = Form.useForm();
  const { onAddPlan, activePlanTab, planInfo, activeTabOptions } = useContext(PlanCreateContext);
  const [isLoading, setIsLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [listTravelSpot, setListTravelSpot] = useState<TouristSpotDetailType[]>([]);
  const [prefectureSelected, setPrefectureSelected] = useState<number | string>();
  const [keyword, setKeyword] = useState<string>('');
  const [paramSearch, setParamSearch] = useState<{
    page: string | number;
    limit: string | number;
    order: string;
    keyword?: string;
    prefecture_code?: string | number;
    city_code?: string | number;
  }>(initialSearch);

  const [cities] = useFetchCity(prefectureSelected);

  const heightScreen = window.innerHeight;

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    try {
      const params = { ...paramSearch };
      const { data, status } = await getListTouristSpot(params);
      if (status === STATUS_CODE.SUCCESSFUL) {
        const dts = data?.data?.map((item, index) => ({
          ...item,
          orderNumber: (Number(paramSearch?.page) - 1) * Number(paramSearch?.limit) + (index + 1),
          prefecture: item?.prefecture?.name_province ?? '',
          city: item?.city?.name_city ?? '',
        }));
        setListTravelSpot(dts);
        setTotal(Number(data?.total ?? 0));
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      openNotificationFail('サーバーエラー');
    }
  }, [paramSearch]);

  const getReferenceType = useCallback(() => {
    return PLAN_TABS[`tab_${activeTabOptions}`];
  }, [activeTabOptions]);

  const getDate = useCallback(() => {
    const startDate = new Date(planInfo?.start_date);
    const nextDate = new Date(startDate);
    nextDate.setDate(startDate.getDate() + activePlanTab - 1);
    return formatDateYMD(nextDate);
  }, [activePlanTab, planInfo]);

  const onAddOption = (touristDetail: TouristSpotDetailType) => {
    onAddPlan({
      id: Date.now(),
      reference_id: touristDetail?.id,
      reference_type: getReferenceType(),
      date: getDate(),
      name_jp: touristDetail?.name_jp,
      name_en: touristDetail?.name_en,
      address: touristDetail?.address,
    });
    // Note: close() function removed as it's not defined in this context
  };

  const onChangePrefectures = (value: string) => {
    if (!value) return;
    form.setFieldValue('city_code', undefined);
    setPrefectureSelected(Number(value));
  };

  const onChangeKeyword = (e: React.ChangeEvent<HTMLInputElement>) => {
    setKeyword(e.target.value);
  };

  const onSearch = (value: any) => {
    setParamSearch({
      ...paramSearch,
      page: 1,
      prefecture_code: value?.prefecture_code,
      city_code: value?.city_code,
      keyword: value?.keyword,
      order: value?.order,
    });
  };

  useEffect(() => {
    fetchData();
  }, [paramSearch]);

  const columns: ColumnsType<TouristSpotDetailType> = [
    {
      title: <div className="leading-[19px] text-[13px] font-medium ">#</div>,
      dataIndex: 'No',
      key: 'No',
      width: 65,
      render: (_, { orderNumber }) => <div className="">{orderNumber}</div>,
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">観光地</div>,
      dataIndex: 'tourName',
      key: 'tourName',
      width: 220,
      render: (_, { name_jp }) => (
        <Tooltip title={name_jp}>
          <div className="flex flex-col">
            <div className="text-sm leading-5 font-medium text-[#383B46]">{name_jp}</div>
            {/* <div className="text-[13px] leading-4 text-[#999EAF]">{name_en}</div> */}
          </div>
        </Tooltip>
      ),
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">都道府県</div>,
      dataIndex: 'prefecture',
      key: 'prefecture',
      width: 90,
      render: (_, { prefecture }) => <div>{prefecture}</div>,
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">市区町村</div>,
      dataIndex: 'city',
      key: 'city',
      width: 130,
      render: (_, { city }) => <div>{city}</div>,
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">駐車場</div>,
      dataIndex: 'parking_lot',
      key: 'parking_lot',
      width: 120,
      render: (_, { parking_lot }) => <div>{parking_lot === 1 ? '有' : '無'}</div>,
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">駐車料金</div>,
      dataIndex: 'parking_fee',
      key: 'parking_fee',
      width: 120,
      render: (_, { parking_fee }) => (
        <div className="text-right">{formatMoney(parking_fee ?? 0)}</div>
      ),
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium text-center" />,
      dataIndex: 'detail',
      key: 'detail',
      width: 100,
      fixed: 'right',
      render: (_, record) => (
        <div className="flex items-center justify-center">
          <div
            className="flex items-center justify-center text-main-color gap-x-1 cursor-pointer"
            onClick={() => onAddOption(record)}
          >
            <PlusOutlined /> 旅程に追加する
          </div>
        </div>
      ),
    },
  ];

  const renderListItems = () => (
    <>
      <BasicTable
        className={`!mt-0 h-full relative`}
        hasPagination={false}
        tableProps={{
          scroll: { y: heightScreen - 400, x: 1200 },
          columns,
          loading: isLoading,
          dataSource: listTravelSpot,
          bordered: false,
          pagination: false,
          rowKey: 'id',
        }}
        page={0}
        pageSize={0}
        onChangePage={null}
        total={0}
        onSelectPageSize={null}
      />
      <div className="flex flex-1 pt-3 justify-center items-center">
        <BasicPagination
          page={Number(paramSearch?.page)}
          pageSize={Number(paramSearch?.limit)}
          onChange={(p: number) => setParamSearch({ ...paramSearch, page: p })}
          total={total}
          onSelectPageSize={(v) => {
            setParamSearch({ ...paramSearch, page: 1, limit: v });
          }}
        />
      </div>
    </>
  );

  useEffect(() => {
    form.setFieldsValue({
      order: sortOrderOption?.[0]?.value,
    });
  }, []);

  return (
    <div className="flex flex-col h-full">
      <Form form={form} onFinish={onSearch}>
        <div className="flex gap-5">
          <div className="flex gap-5 [&_.ant-select-selector]:!h-9">
            <Form.Item name="prefecture_code" className="w-[140px]">
              <BasicSelect
                allowClear
                title="都道府県"
                placeholder="すべて"
                onClear={() => form.setFieldValue('city_code', undefined)}
                options={PREFECTURES_OPTIONS}
                onChange={onChangePrefectures}
                showSearch
                filterOption={(input, option) => {
                  return option?.props?.label?.toLowerCase().indexOf(input?.toLowerCase()) >= 0;
                }}
              />
            </Form.Item>
            <Form.Item name="city_code" className="w-[140px]">
              <BasicSelect
                allowClear
                title="市区町村"
                placeholder="すべて"
                showSearch
                filterOption={(input, option) => {
                  return (
                    (option?.children as any)?.toLowerCase().indexOf(input?.toLowerCase()) >= 0
                  );
                }}
              >
                {cities?.map((city) => (
                  <Select.Option key={city.id} value={city.id}>
                    {city.name_city}
                  </Select.Option>
                ))}
              </BasicSelect>
            </Form.Item>

            <Form.Item name={'order'} className="w-[140px]">
              <BasicSelect
                title="並べ替え"
                className="min-w-[140px] [&_.ant-select-selector]:flex [&_.ant-select-selector]:items-center"
                options={sortOrderOption}
              />
            </Form.Item>

            <Form.Item name="keyword">
              <BasicInput
                value={keyword}
                title="観光地"
                placeholder="キーワードで検索する"
                onChange={onChangeKeyword}
                allowClear
                className="!h-9 max-w-[300px]"
              />
            </Form.Item>
          </div>
          <div className="flex items-center">
            <BasicButton
              htmlType="submit"
              styleType="accept"
              className="flex items-center justify-center border-main-color w-[84px] space-x-[8px] !h-9"
            >
              <SearchSVG colorSvg="white" />
              検索
            </BasicButton>
          </div>
        </div>
      </Form>
      {renderListItems()}
    </div>
  );
}

export default TravelSpot;
