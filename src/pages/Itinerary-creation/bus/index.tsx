import type { BaseParams } from '@/@types/request';
import type { BusDetailType } from '@/apis/purchase-and-material-management/buses';
import { getListBus } from '@/apis/purchase-and-material-management/buses';
import BasicButton from '@/components/Commons/BasicButton';
import BasicInput from '@/components/Commons/BasicInput';
import BasicPagination from '@/components/Commons/BasicPagination';
import BasicSelect from '@/components/Commons/BasicSelect';
import BasicTable from '@/components/Commons/BasicTable';
import { openNotificationFail } from '@/components/Notification';
import SearchSVG from '@/components/SVG/SearchSVG';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { PLAN_TABS } from '@/constants/plan';
import STATUS_CODE from '@/constants/statusCode';
import { CarModelShortName } from '@/pages/purchase-and-material-management/buses/components/BusFormFieldItem';
import { PlanCreateContext } from '@/providers';
import { ITEM_PER_PAGE, sortOrderOption } from '@/utils/constants';
import { formatDateYMD } from '@/utils/date';
import { PlusOutlined } from '@ant-design/icons';
import { Checkbox, Form, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import React, { useCallback, useContext, useEffect, useState } from 'react';

const initialSearch = {
  page: 1,
  limit: ITEM_PER_PAGE,
  order: sortOrderOption?.[0]?.value,
  sort: 'name_jp',
};

const BusTab = () => {
  const [form] = Form.useForm();
  const { onAddPlan, activePlanTab, planInfo, activeTabOptions } = useContext(PlanCreateContext);
  const [isLoading, setIsLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [listTravelSpot, setListTravelSpot] = useState<BusDetailType[]>([]);
  const [keyword, setKeyword] = useState<string>('');
  const [paramSearch, setParamSearch] = useState<{
    page: string | number;
    limit: string | number;
    order: string;
    keyword?: string;
  }>(initialSearch);

  const heightScreen = window.innerHeight;

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    try {
      const params = { ...paramSearch };
      const { data, status } = await getListBus(params);
      if (status === STATUS_CODE.SUCCESSFUL) {
        const dts = data?.data?.map((item, index) => ({
          ...item,
          orderNumber: (Number(paramSearch?.page) - 1) * Number(paramSearch?.limit) + (index + 1),
          prefecture: item?.prefecture?.name_province ?? '',
          city: item?.city?.name_city ?? '',
        }));
        setListTravelSpot(dts);
        setTotal(Number(data?.total ?? 0));
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      openNotificationFail('サーバーエラー');
    }
  }, [paramSearch]);

  const getReferenceType = useCallback(() => {
    return PLAN_TABS[`tab_${activeTabOptions}`];
  }, [activeTabOptions]);

  const getDate = useCallback(() => {
    const startDate = new Date(planInfo?.start_date);
    const nextDate = new Date(startDate);
    nextDate.setDate(startDate.getDate() + activePlanTab - 1);
    return formatDateYMD(nextDate);
  }, [activePlanTab, planInfo]);

  const onAddOption = (record: BusDetailType) => {
    onAddPlan({
      id: Date.now(),
      reference_id: record?.id,
      reference_type: getReferenceType(),
      date: getDate(),
      name_jp: record?.name_jp,
      name_en: record?.name_en,
      address: record?.address,
    });
    close();
  };

  const onChangeKeyword = (e: React.ChangeEvent<HTMLInputElement>) => {
    setKeyword(e.target.value);
  };

  const onSearch = async (value) => {
    setParamSearch({
      ...paramSearch,
      page: 1,
      keyword: value?.keyword,
      order: value?.order,
    });
  };

  useEffect(() => {
    fetchData();
  }, [paramSearch]);

  const columns: ColumnsType<BusDetailType> = [
    {
      title: <div className="leading-[19px] text-[13px] font-medium text-center">#</div>,
      dataIndex: 'No',
      key: 'No',
      width: 60,
      render: (value, { orderNumber }) => <div className="text-center">{orderNumber}</div>,
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">バス</div>,
      dataIndex: 'name_jp',
      key: 'name_jp',
      render: (_, { name_jp }) => (
        <Tooltip title={name_jp}>
          <div className="flex flex-col">
            <div className="text-sm leading-5 font-medium text-[#383B46]">{name_jp}</div>
          </div>
        </Tooltip>
      ),
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium ">対応エリア</div>,
      dataIndex: 'area',
      key: 'area',
      render: (value, { area }) => <div className="">{area}</div>,
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium ">車種</div>,
      dataIndex: 'vehicle_type',
      key: 'vehicle_type',
      width: 360,
      render: (value, { vehicle_type }) => (
        <div className="">
          {CarModelShortName?.map((item) => (
            <Checkbox
              value={item.value}
              key={item.value}
              checked={vehicle_type.includes(item.value)}
            >
              {item.label}
            </Checkbox>
          ))}
        </div>
      ),
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium text-center" />,
      dataIndex: 'detail',
      key: 'detail',
      width: 160,
      render: (value, record) => (
        <div className="flex items-center justify-center">
          <div
            className="flex items-center justify-center text-main-color gap-x-1"
            onClick={() => onAddOption(record)}
          >
            <PlusOutlined /> 旅程に追加する
          </div>
        </div>
      ),
    },
  ];

  const renderListItems = () => (
    <>
      <BasicTable
        className={`!mt-0 h-full`}
        hasPagination={false}
        tableProps={{
          scroll: { y: heightScreen - 400 },
          columns,
          loading: isLoading,
          dataSource: listTravelSpot,
          bordered: false,
          pagination: false,
          rowKey: 'id',
        }}
        page={0}
        pageSize={0}
        onChangePage={null}
        total={0}
        onSelectPageSize={null}
      />
      <div className="flex flex-1 pt-3 justify-center items-center">
        <BasicPagination
          page={Number(paramSearch?.page)}
          pageSize={Number(paramSearch?.limit)}
          onChange={(p: number) => setParamSearch({ ...paramSearch, page: p })}
          total={total}
          onSelectPageSize={(v) => {
            setParamSearch({ ...paramSearch, page: 1, limit: v });
          }}
        />
      </div>
    </>
  );

  useEffect(() => {
    form.setFieldsValue({
      order: sortOrderOption?.[0]?.value,
    });
  }, []);

  return (
    <div className="flex flex-col h-full">
      <Form form={form} onFinish={onSearch}>
        <div className="flex gap-5">
          <div className="flex gap-5 [&_.ant-select-selector]:!h-9">
            <Form.Item name={'order'} className="w-[140px]">
              <BasicSelect
                title="並べ替え"
                className="min-w-[140px] [&_.ant-select-selector]:flex [&_.ant-select-selector]:items-center"
                options={sortOrderOption}
              />
            </Form.Item>

            <Form.Item name="keyword">
              <BasicInput
                value={keyword}
                title="バス"
                placeholder="キーワードで検索する"
                onChange={onChangeKeyword}
                allowClear
                className="!h-9 max-w-[300px]"
              />
            </Form.Item>
          </div>
          <div className="flex items-center">
            <BasicButton
              htmlType="submit"
              styleType="accept"
              className="flex items-center justify-center border-main-color w-[84px] space-x-[8px] !h-9"
            >
              <SearchSVG colorSvg="white" />
              検索
            </BasicButton>
          </div>
        </div>
      </Form>
      {renderListItems()}
    </div>
  );
};

export default BusTab;
